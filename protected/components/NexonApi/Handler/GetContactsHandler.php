<?php

namespace Components\NexonApi\Handler;

use Components\NexonApi\Enum\ConnectEnum;
use Components\NexonApi\Enum\NexonApiCallNameEnum;
use Components\NexonApi\Interfaces\ApiCallHandlerInterface;
use Components\NexonApi\Provider\DataProvider;

final class GetContactsHandler implements ApiCallHandlerInterface
{
    private const GETCONTACTS = '/OdataApi/v5.0/GetContacts';
    private DataProvider $dataProvider;

    public function __construct()
    {
        $this->dataProvider = new DataProvider();
    }

    public function handle($params): string
    {
        $rawData = '';
        if (isset($params[ConnectEnum::PERSONALIDS]) && !empty($params[ConnectEnum::PERSONALIDS])) {
            $params[ConnectEnum::APICALLURL] = self::GETCONTACTS;
            $params[ConnectEnum::BODY] = '"personids": [' . $params[ConnectEnum::PERSONALIDS] . ']';
            $rawData = $this->dataProvider->provide($params);
        }

        return $rawData;
    }

    public function getName(): string
    {
        return NexonApiCallNameEnum::GET_CONTACTS;
    }
}
