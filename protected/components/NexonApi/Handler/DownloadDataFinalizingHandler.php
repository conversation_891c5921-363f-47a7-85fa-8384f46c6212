<?php 

namespace Components\NexonApi\Handler;

use Components\NexonApi\Descriptor\NexonRawDataDescriptor;
use Components\NexonApi\Enum\WorkflowEnum;
use Components\NexonApi\Enum\ConnectEnum;
use Components\NexonApi\Interfaces\ApiCallHandlerInterface;

final class DownloadDataFinalizingHandler
{
    private NexonDataWritingHandler $writer;
    /**
     * @var ApiCallHandlerInterface[]
     */
    private array $handlers;

    public function __construct()
    {
        $this->writer = new NexonDataWritingHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetPersons2Handler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetPersonNameHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetHrRelationshipHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetHrRelationshipDictionaryHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetPositionFulfillmentHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetPositionFulfillmentDictionaryHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetPositionsHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetOrgUnits3Handler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetAnnualLeaveHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetEmploymentContractHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetEmployeeContractTypeDictionaryHandler();
        $this->handlers[WorkflowEnum::EMPLOYEEALLDATA][] = new GetLongAbsenceHandler();
    }
    public function handle(array $params) : void
    {
        $processId = \App::getIncreasedGlobalIdentifier(ConnectEnum::PROCESS_ID);
        $workflow = $params[ConnectEnum::WORKFLOW];
        /** @var ApiCallHandlerInterface $handler */
        foreach($this->handlers[$workflow] as $handler)
        {
            $desciptor = new NexonRawDataDescriptor($handler->handle($params));
            $desciptor->setWorkflow($workflow);
            $desciptor->setHandlerName($handler->getName());
            $desciptor->setProcessId($processId);
            $this->writer->handle($desciptor);
        }
    }

}
