<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Loader;

use Flow\ETL\FlowContext;
use Flow\ETL\Loader;
use Flow\ETL\Rows;

final readonly class ModelsInCacheToDbLoader implements Loader
{
    public function __construct(
        private array $cacheName
    ) {
    }
    public function load(Rows $rows, FlowContext $context): void
    {
        foreach ($this->cacheName as $cache) {
            $rowsArray = iterator_to_array($context->cache()->read($cache));
            /** @var Rows $modelRows */
            $modelRows = reset($rowsArray);
            if (!$modelRows) {
                continue;
            }
            foreach ($modelRows as $entries) {
                $modelArrays = $entries->toArray();
                foreach ($modelArrays as $model) {
                    if ($model->validate()) {
                        $model->save();
                    }
                }
            }
        }
    }

    public function __serialize(): array
    {
        return [
            'cacheName' => $this->cacheName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->cacheName = $data['cacheName'];
    }
}
